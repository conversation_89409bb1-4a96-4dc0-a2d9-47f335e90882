"""
Master Router for Interactive Agents

This module provides the master router for the AiLex interactive agents system.
The master router analyzes user input and routes requests to the appropriate
specialized agent or graph based on intent detection.

The router supports routing to:
- Calendar CRUD Agent: For calendar-related operations (create, read, update,
  delete events)
  * Detects calendar intents using keywords and patterns
  * Routes to calendar_graph for comprehensive calendar management
  * Supports natural language calendar operations
- Task CRUD Agent: For task management operations
- Case & Client CRUD Agent: For case and client management
- Research Agent: For legal research queries
- Intake Agent: For new client intake processes
- Document Agent: For legal document generation and management
  * Detects document intents for drafting, editing, and reviewing
  * Supports demand letters, settlement agreements, court filings
  * Routes to document_agent for document operations
- Deadline Agent: For deadline tracking and calculation
  * Detects deadline intents for statute of limitations, court deadlines
  * Supports deadline calculations, notifications, and tracking
  * Routes to deadline_agent for deadline management

Calendar Intent Detection:
The router uses sophisticated intent detection for calendar operations, including:
- Schedule/booking keywords: "schedule", "book", "create meeting", "set appointment"
- View/read keywords: "show calendar", "view calendar", "my schedule"
- Update keywords: "reschedule", "move meeting", "change appointment"
- Delete keywords: "cancel meeting", "delete event", "remove appointment"
- Availability keywords: "when am i free", "available time", "find time"
- Explicit namespace: "calendar.create", "calendar.list", etc.

Usage:
    from backend.agents.interactive.master_router import (
        master_router, create_master_graph
    )

    # Use in a LangGraph StateGraph
    sg.add_node("masterRouter", master_router)

    # Or create a complete master graph
    graph = create_master_graph(voyage=voyage_client)

    # Example routing scenarios:
    # "Schedule a meeting tomorrow" → calendar_graph
    # "matter.create new matter" → matter_client_agent
    # "Research personal injury law" → research_agent
    # "Draft a demand letter" → document_agent
    # "Check statute of limitations" → deadline_agent
"""

import logging
import re
from typing import Any, Dict, Literal, TypedDict

from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.calendar_crud.graph import create_calendar_graph
from backend.agents.interactive.task_crud.graph import create_task_graph
from shared.core.llm.voyage import VoyageClient

# Set up logging
logger = logging.getLogger(__name__)

# Define the router output type
class MasterRouterOutput(TypedDict):
    next: Literal[
        "calendar_graph",
        "task_graph",
        "matter_client_agent",
        "research_agent",
        "intake_agent",
        "document_agent",
        "deadline_agent",
        "supervisor_agent"
    ]

# Calendar intent keywords for routing
CALENDAR_KEYWORDS = [
    # Event creation
    "schedule", "book", "create meeting", "set appointment", "add event",
    "plan meeting", "arrange", "organize meeting",
    
    # Event reading/viewing
    "show calendar", "view calendar", "check calendar", "my schedule",
    "what's on my calendar", "calendar events", "upcoming meetings",
    "today's schedule", "tomorrow's schedule", "this week's schedule",
    
    # Event updating
    "reschedule", "move meeting", "change appointment", "update event",
    "modify meeting", "edit appointment",
    
    # Event deletion
    "cancel meeting", "delete event", "remove appointment", "cancel appointment",
    
    # Free/busy checking
    "when am i free", "available time", "free time", "busy time",
    "check availability", "find time", "when can we meet"
]

# Calendar intent patterns (regex)
CALENDAR_PATTERNS = [
    r"calendar\.",  # Explicit calendar namespace
    r"schedule.*(?:meeting|appointment|event)",
    r"(?:create|add|book).*(?:meeting|appointment|event)",
    r"(?:show|view|check).*calendar",
    r"(?:reschedule|move|change).*(?:meeting|appointment)",
    r"(?:cancel|delete|remove).*(?:meeting|appointment|event)",
    r"when.*(?:free|available|busy)",
    r"find.*time.*(?:meeting|meet)"
]


async def master_router(state: Dict[str, Any],
                       config: RunnableConfig) -> MasterRouterOutput:
    """
    Master router node for interactive agents.
    
    This node analyzes user input and determines which specialized agent
    or graph should handle the request based on intent detection.
    
    Args:
        state: Agent state containing messages and context
        config: Runnable configuration
        
    Returns:
        MasterRouterOutput: Next agent/graph to execute
    """
    logger.info("Master router analyzing user input for intent detection")
    
    # Get the user input from messages
    messages = state.get("messages", [])
    if not messages:
        logger.info("No messages found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}
    
    # Find the last human message
    user_input = ""
    for message in reversed(messages):
        if (isinstance(message, HumanMessage) or
            (hasattr(message, "type") and message.type == "human")):
            user_input = (message.content if hasattr(message, 'content')
                         else str(message))
            break
        elif isinstance(message, dict) and message.get("type") == "human":
            user_input = message.get("content", "")
            break
    
    if not user_input:
        logger.info("No user input found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}
    
    logger.info(f"Analyzing user input: {user_input[:100]}...")
    
    # Check for calendar intent
    if _is_calendar_intent(user_input):
        logger.info("Calendar intent detected, routing to calendar_graph")
        return {"next": "calendar_graph"}
    
    # Check for explicit agent routing (e.g., "case.create", "task.list")
    # Explicit namespaces take priority over keyword detection
    if (user_input.lower().startswith("matter.") or
        user_input.lower().startswith("client.") or
        user_input.lower().startswith("case.")):
        logger.info("Matter/Client intent detected, routing to matter_client_agent")
        return {"next": "matter_client_agent"}

    # Check for explicit task namespace first
    if user_input.lower().startswith("task."):
        logger.info("Task intent detected (explicit namespace), routing to task_graph")
        return {"next": "task_graph"}

    # Check for explicit document namespace first
    if user_input.lower().startswith("document."):
        logger.info("Document intent detected (explicit namespace), routing to document_agent")
        return {"next": "document_agent"}

    # Check for explicit deadline namespace first
    if user_input.lower().startswith("deadline."):
        logger.info("Deadline intent detected (explicit namespace), routing to deadline_agent")
        return {"next": "deadline_agent"}

    # Now check for keyword-based intents (less priority than explicit namespaces)
    # Check deadline intent first as it's more specific than document intent
    if _is_deadline_intent(user_input):
        logger.info("Deadline intent detected, routing to deadline_agent")
        return {"next": "deadline_agent"}

    if _is_document_intent(user_input):
        logger.info("Document intent detected, routing to document_agent")
        return {"next": "document_agent"}

    if _is_task_intent(user_input):
        logger.info("Task intent detected, routing to task_graph")
        return {"next": "task_graph"}

    # Check for research keywords
    research_keywords = ["research", "find", "search", "lookup", "legal",
                        "case law", "statute"]
    if any(keyword in user_input.lower() for keyword in research_keywords):
        logger.info("Research intent detected, routing to research_agent")
        return {"next": "research_agent"}

    # Check for intake keywords (for new matters)
    intake_keywords = ["new client", "intake", "new case", "new matter",
                      "client information"]
    if any(keyword in user_input.lower() for keyword in intake_keywords):
        logger.info("Intake intent detected, routing to intake_agent")
        return {"next": "intake_agent"}

    # Default to supervisor agent for complex routing decisions
    logger.info("No specific intent detected, routing to supervisor_agent")
    return {"next": "supervisor_agent"}


def _is_calendar_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates calendar-related intent.
    
    Args:
        user_input: The user's input text
        
    Returns:
        bool: True if calendar intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()
    
    # Check for explicit calendar keywords
    for keyword in CALENDAR_KEYWORDS:
        if keyword in user_input_lower:
            logger.debug(f"Calendar keyword '{keyword}' found in user input")
            return True
    
    # Check for calendar patterns using regex
    for pattern in CALENDAR_PATTERNS:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Calendar pattern '{pattern}' matched in user input")
            return True
    
    return False


def _is_task_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates task-related intent.

    Args:
        user_input: The user's input text

    Returns:
        bool: True if task intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()

    # Task intent keywords for routing
    task_keywords = [
        # Task creation
        "create task", "add task", "new task", "make task", "schedule task",

        # Task reading/viewing
        "show tasks", "view tasks", "list tasks", "get tasks", "my tasks",
        "find task", "search task", "retrieve task", "search for task",

        # Task updating
        "update task", "change task", "modify task", "edit task",
        "mark task", "complete task", "finish task",

        # Task deletion
        "delete task", "remove task", "cancel task"
    ]

    # Check for task keywords
    for keyword in task_keywords:
        if keyword in user_input_lower:
            logger.debug(f"Task keyword '{keyword}' found in user input")
            return True

    # Task intent patterns (regex)
    task_patterns = [
        r"(?:create|add|make|new).*task",
        r"(?:show|view|list|get).*tasks?",
        r"(?:update|change|modify|edit).*task",
        r"(?:delete|remove|cancel).*task",
        r"mark.*task.*(?:done|complete|finished)",
        r"task.*(?:create|add|update|delete|list|show)"
    ]

    # Check for task patterns using regex
    for pattern in task_patterns:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Task pattern '{pattern}' matched in user input")
            return True

    return False


def _is_document_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates document-related intent.

    Args:
        user_input: The user's input text

    Returns:
        bool: True if document intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()

    # Document intent keywords for routing
    document_keywords = [
        # Document creation (specific phrases)
        "create document", "generate document", "draft document", "write document",
        "new document", "make document", "compose document",

        # Document types (specific legal documents)
        "demand letter", "settlement agreement", "court filing", "legal brief",
        "contract", "agreement", "motion", "pleading", "complaint", "answer",
        "discovery request", "deposition notice", "subpoena",

        # Document actions (specific to documents)
        "draft letter", "draft agreement", "draft contract", "draft motion",
        "write letter", "write agreement", "write contract", "write brief", "write document",
        "compose letter", "compose agreement", "compose motion",
        "generate letter", "generate agreement", "generate contract",
        "edit document", "update document", "revise document", "modify document",
        "review document", "check document", "proofread document", "check the document",

        # Document management
        "document template", "legal template", "legal form"
    ]

    # Check for document keywords
    for keyword in document_keywords:
        if keyword in user_input_lower:
            logger.debug(f"Document keyword '{keyword}' found in user input")
            return True

    # Document intent patterns (regex)
    document_patterns = [
        r"document\.",  # Explicit document namespace
        r"(?:draft|write|compose|generate).*(?:letter|agreement|contract|motion|brief|filing|pleading|document)",
        r"(?:demand|settlement|court|legal).*(?:letter|agreement|filing|brief|document)",
        r"(?:edit|update|revise|modify).*(?:document|letter|agreement|contract)",
        r"(?:create|generate|make).*(?:document|letter|agreement|contract|motion|brief)",
        r"document.*(?:template|form)",
        r"legal.*document"
    ]

    # Check for document patterns using regex
    for pattern in document_patterns:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Document pattern '{pattern}' matched in user input")
            return True

    return False


def _is_deadline_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates deadline-related intent.

    Args:
        user_input: The user's input text

    Returns:
        bool: True if deadline intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()

    # Deadline intent keywords for routing
    deadline_keywords = [
        # Deadline tracking
        "deadline", "due date", "statute of limitations", "filing deadline",
        "court deadline", "discovery deadline", "response deadline",

        # Deadline actions
        "check deadlines", "upcoming deadlines", "deadline reminder",
        "calculate deadline", "deadline calculation", "when is due",
        "deadline tracker", "track deadline", "monitor deadline",

        # Legal deadlines
        "statute", "limitation period", "time limit", "filing period",
        "response time", "discovery cutoff", "trial date", "hearing date",
        "motion deadline", "appeal deadline", "settlement deadline",

        # Deadline management
        "deadline alert", "deadline notification", "deadline warning",
        "missed deadline", "approaching deadline", "overdue"
    ]

    # Check for deadline keywords
    for keyword in deadline_keywords:
        if keyword in user_input_lower:
            logger.debug(f"Deadline keyword '{keyword}' found in user input")
            return True

    # Deadline intent patterns (regex)
    deadline_patterns = [
        r"deadline\.",  # Explicit deadline namespace
        r"(?:check|show|view|list|get).*deadline",
        r"(?:calculate|compute|determine).*(?:deadline|due.*date|statute)",
        r"(?:statute|limitation).*(?:period|deadline|date)",
        r"(?:filing|court|discovery|response).*(?:deadline|due|date)",
        r"deadline.*(?:check|calculate|track|monitor|alert|reminder)",
        r"when.*(?:due|deadline|expires|statute)",
        r"(?:upcoming|approaching|missed|overdue).*deadline"
    ]

    # Check for deadline patterns using regex
    for pattern in deadline_patterns:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Deadline pattern '{pattern}' matched in user input")
            return True

    return False


def create_master_graph(*, voyage: VoyageClient) -> Any:
    """
    Create the master graph that routes to specialized agents and graphs.

    This function builds a LangGraph StateGraph that implements the master routing
    workflow, connecting the master router to various specialized agents including
    the calendar graph.

    Args:
        voyage: VoyageClient instance for LLM operations

    Returns:
        StateGraph: Compiled master routing workflow graph
    """
    from langgraph.graph import END, StateGraph

    logger.info("Creating master routing StateGraph workflow")

    # Create the graph with Dict[str, Any] as the state type
    workflow = StateGraph(Dict[str, Any])

    # Add the master router node
    workflow.add_node("master_router", master_router)

    # Create and add the calendar graph
    calendar_graph = create_calendar_graph(voyage=voyage)
    workflow.add_node("calendar_graph", calendar_graph)

    # Create and add the task graph
    task_graph = create_task_graph(voyage=voyage)
    workflow.add_node("task_graph", task_graph)

    # Add placeholder nodes for other agents (to be implemented)
    # These would be replaced with actual agent implementations
    async def placeholder_agent(state: Dict[str, Any],
                               config: RunnableConfig) -> Dict[str, Any]:
        """Placeholder agent implementation."""
        agent_name = state.get("_current_agent", "unknown")
        logger.info(f"Placeholder agent '{agent_name}' executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": (f"This is a placeholder response from {agent_name}. "
                       "Implementation pending.")
        }]
        return state

    # Document agent placeholder (will be replaced with actual implementation)
    async def document_agent_placeholder(state: Dict[str, Any],
                                       config: RunnableConfig) -> Dict[str, Any]:
        """Document agent placeholder implementation."""
        logger.info("Document agent placeholder executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": ("Document agent is ready to help with legal document generation. "
                       "I can assist with demand letters, settlement agreements, court filings, "
                       "and other legal documents. Full implementation coming soon.")
        }]
        return state

    # Deadline agent placeholder (will be replaced with actual implementation)
    async def deadline_agent_placeholder(state: Dict[str, Any],
                                       config: RunnableConfig) -> Dict[str, Any]:
        """Deadline agent placeholder implementation."""
        logger.info("Deadline agent placeholder executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": ("Deadline agent is ready to help with deadline tracking and calculations. "
                       "I can assist with statute of limitations, court deadlines, discovery deadlines, "
                       "and deadline notifications. Full implementation coming soon.")
        }]
        return state

    workflow.add_node("matter_client_agent", placeholder_agent)
    workflow.add_node("research_agent", placeholder_agent)
    workflow.add_node("intake_agent", placeholder_agent)
    workflow.add_node("document_agent", document_agent_placeholder)
    workflow.add_node("deadline_agent", deadline_agent_placeholder)
    workflow.add_node("supervisor_agent", placeholder_agent)

    # Set the entry point
    workflow.set_entry_point("master_router")

    # Add conditional edges from master router to specialized agents
    workflow.add_conditional_edges(
        "master_router",
        lambda state: state.get("next", "supervisor_agent"),
        {
            "calendar_graph": "calendar_graph",
            "task_graph": "task_graph",
            "matter_client_agent": "matter_client_agent",
            "research_agent": "research_agent",
            "intake_agent": "intake_agent",
            "document_agent": "document_agent",
            "deadline_agent": "deadline_agent",
            "supervisor_agent": "supervisor_agent",
        }
    )

    # Add edges from all agents to END
    workflow.add_edge("calendar_graph", END)
    workflow.add_edge("task_graph", END)
    workflow.add_edge("matter_client_agent", END)
    workflow.add_edge("research_agent", END)
    workflow.add_edge("intake_agent", END)
    workflow.add_edge("document_agent", END)
    workflow.add_edge("deadline_agent", END)
    workflow.add_edge("supervisor_agent", END)

    # Compile the workflow
    compiled_workflow = workflow.compile()

    logger.info("Master routing StateGraph workflow created and compiled successfully")
    return compiled_workflow


def get_workflow_info() -> Dict[str, Any]:
    """
    Get information about the master routing workflow.

    Returns:
        Dict[str, Any]: Workflow information including nodes, edges, and capabilities
    """
    return {
        "name": "master_routing_workflow",
        "description": "LangGraph workflow for routing to specialized agents",
        "version": "1.0.0",
        "nodes": [
            "master_router",
            "calendar_graph",
            "task_graph",
            "matter_client_agent",
            "research_agent",
            "intake_agent",
            "document_agent",
            "deadline_agent",
            "supervisor_agent"
        ],
        "entry_point": "master_router",
        "capabilities": [
            "Intent detection and routing",
            "Calendar operations routing",
            "Task management routing",
            "Case and client management routing",
            "Legal research routing",
            "Client intake routing",
            "Document generation routing",
            "Deadline tracking routing",
            "Fallback to supervisor agent"
        ],
        "supported_intents": [
            "calendar.*",
            "task.*",
            "case.*",
            "client.*",
            "document.*",
            "deadline.*",
            "research queries",
            "intake processes"
        ]
    }
